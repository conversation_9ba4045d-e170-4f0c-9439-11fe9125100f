#!/bin/bash
# 调试脚本 - 验证startup_script的内联内容

echo "🔍 调试 Coder 工作空间初始化脚本"
echo "=================================="

# 检查当前目录和文件
echo "📁 当前工作目录: $(pwd)"
echo "📁 目录内容:"
ls -la

# 检查脚本文件是否存在（应该不存在，因为我们使用内联方式）
echo ""
echo "📋 检查脚本文件存在性:"
if [ -f "./scripts/system_setup.sh" ]; then
    echo "✅ system_setup.sh 存在"
else
    echo "❌ system_setup.sh 不存在 (这是预期的，因为使用内联方式)"
fi

if [ -f "./scripts/juicefs_setup.sh" ]; then
    echo "✅ juicefs_setup.sh 存在"
else
    echo "❌ juicefs_setup.sh 不存在 (这是预期的，因为使用内联方式)"
fi

# 检查系统环境
echo ""
echo "🖥️  系统环境检查:"
echo "   操作系统: $(lsb_release -ds 2>/dev/null || echo '未知')"
echo "   内核版本: $(uname -r)"
echo "   架构: $(dpkg --print-architecture 2>/dev/null || uname -m)"

# 检查网络连通性
echo ""
echo "🌐 网络连通性检查:"
if ping -c 1 ******* >/dev/null 2>&1; then
    echo "✅ 基础网络连通正常"
else
    echo "❌ 基础网络连通失败"
fi

if curl -s --connect-timeout 5 https://download.docker.com >/dev/null 2>&1; then
    echo "✅ Docker官方源可访问"
else
    echo "❌ Docker官方源不可访问"
fi

# 检查包管理器状态
echo ""
echo "📦 包管理器状态:"
if apt-get update -qq 2>/dev/null; then
    echo "✅ apt-get update 成功"
else
    echo "❌ apt-get update 失败"
fi

# 检查关键命令是否存在
echo ""
echo "🔧 关键命令检查:"
commands=("curl" "wget" "gpg" "systemctl" "docker")
for cmd in "${commands[@]}"; do
    if command -v "$cmd" >/dev/null 2>&1; then
        echo "✅ $cmd: $(command -v "$cmd")"
    else
        echo "❌ $cmd: 未找到"
    fi
done

# 检查Docker服务状态（如果已安装）
if command -v docker >/dev/null 2>&1; then
    echo ""
    echo "🐳 Docker状态检查:"
    echo "   版本: $(docker --version 2>/dev/null || echo '版本检查失败')"
    if systemctl is-active docker >/dev/null 2>&1; then
        echo "✅ Docker服务运行中"
    else
        echo "❌ Docker服务未运行"
    fi
fi

# 检查JuiceFS相关
echo ""
echo "💾 JuiceFS环境检查:"
if command -v juicefs >/dev/null 2>&1; then
    echo "✅ JuiceFS已安装: $(juicefs version 2>/dev/null | head -1 || echo '版本检查失败')"
else
    echo "❌ JuiceFS未安装"
fi

if [ -d "/data/workspaces" ]; then
    if mountpoint -q /data/workspaces 2>/dev/null; then
        echo "✅ JuiceFS已挂载到 /data/workspaces"
        echo "   使用情况: $(df -h /data/workspaces | awk 'NR==2 {print $3"/"$2" ("$5")"}')"
    else
        echo "⚠️  /data/workspaces 目录存在但未挂载"
    fi
else
    echo "❌ /data/workspaces 目录不存在"
fi

echo ""
echo "🎯 调试建议:"
echo "1. 如果脚本文件不存在但功能正常，说明内联方案工作正常"
echo "2. 如果Docker命令未找到，检查Docker安装过程的错误日志"
echo "3. 如果JuiceFS未挂载，检查网络连通性和认证参数"
echo "4. 查看详细日志: journalctl -u coder-agent -f"

echo ""
echo "📋 调试完成!"
