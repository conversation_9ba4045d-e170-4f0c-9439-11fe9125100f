terraform {
  required_providers {
    tencentcloud = {
      source  = "tencentcloudstack/tencentcloud"
      version = "1.82.23"
    }
    coder = {
      source  = "coder/coder"
      version = "2.11.0"
    }
  }
}

# 配置腾讯云 Provider
provider "tencentcloud" {
  secret_id  = var.tencentcloud_cvm_secret_id
  secret_key = var.tencentcloud_cvm_secret_key
  region     = local.region
}

# 配置 Coder Provider
provider "coder" {}

# 配置 Coder Agent - 支持远程开发工具连接
resource "coder_agent" "main" {
  arch = "amd64"  # 目标架构
  os   = "linux"  # 目标操作系统
  startup_script_behavior = "blocking"  # 阻塞模式: 启动脚本必须完成后工作空间才可用, 确保环境完全初始化.
  dir = "/data/workspaces"  # 用户连接时的默认工作目录, 直接进入 JuiceFS 挂载的共享工作空间.
  connection_timeout = 300  # Agent 连接超时时间 (秒) 给予充足时间完成 JuiceFS 挂载和环境初始化

  # 配置 Coder Web UI 中显示的基础工具
  display_apps {
    vscode                  = false  # 禁用 VSCode: 专注于 JetBrains 开发体验
    vscode_insiders         = false  # 禁用 VSCode Insiders
    web_terminal            = false  # 禁用 Web 终端: 简化界面专注于 IDE 集成
    ssh_helper              = true   # 启用 SSH 助手: 提供故障排查和直接连接支持
    port_forwarding_helper  = true   # 启用端口转发助手: 便于调试 Web 服务和数据库连接
  }

  # 模块化的环境初始化脚本
  startup_script = <<-EOT
    #!/bin/bash
    set -e

    echo "🚀 开始初始化 Coder 工作空间..."

    # 第一阶段: 系统环境配置
    echo "🔧 第一阶段: 配置系统开发环境..."
    bash ${path.module}/scripts/system_setup.sh

    # 第二阶段: JuiceFS 配置
    echo "💾 第二阶段: 配置 JuiceFS 云服务..."
    bash ${path.module}/scripts/juicefs_setup.sh \
        "${var.juicefs_vol_name}" \
        "${var.juicefs_token}" \
        "${var.tencentcloud_cos_secret_id}" \
        "${var.tencentcloud_cos_secret_key}" \
        "${var.tencentcloud_cos_endpoint}"

    # 显示最终系统信息
    echo ""
    echo "📊 系统信息总览:"
    echo "   CPU: $(nproc) 核心"
    echo "   内存: $(free -h | awk '/^Mem:/ {print $2}')"
    echo "   磁盘: $(df -h / | awk 'NR==2 {print $2}')"
    echo "   JuiceFS: $(df -h /data/workspaces | awk 'NR==2 {print $2}') 可用"
    echo ""
    echo "🎉 Coder 工作空间初始化完成!"
    echo "🚀 开发环境已就绪, 开始您的编程之旅吧!"
  EOT

  # 环境变量配置
  env = {
    WORKSPACE_DIR = "/data/workspaces"
    JUICEFS_MOUNT = "/data/workspaces"
  }

  # 元数据配置
  metadata {
    display_name = "工作空间状态"
    key          = "workspace_status"
    script       = "echo '✅ 运行中'"
    interval     = 60
    timeout      = 10
  }

  metadata {
    display_name = "JuiceFS 挂载状态"
    key          = "juicefs_status"
    script       = "mountpoint -q /data/workspaces && echo '✅ 已挂载' || echo '❌ 未挂载'"
    interval     = 30
    timeout      = 5
  }

  metadata {
    display_name = "磁盘使用情况"
    key          = "disk_usage"
    script       = "df -h /data/workspaces | awk 'NR==2 {print $3\"/\"$2\" (\"$5\")\"} END {if(NR==1) print \"N/A\"}'"
    interval     = 60
    timeout      = 10
  }
}

# ==================== JetBrains 开发环境集成 ====================
#
# 注意: 推荐使用 JetBrains Gateway 的 Coder 插件进行连接
# 插件下载: https://plugins.jetbrains.com/plugin/19620-coder
#
# 配置步骤:
# 1. 在 JetBrains Gateway 中安装 Coder 插件
# 2. 配置 Coder Server 地址和认证信息
# 3. 选择工作空间直接连接 (无需 SSH 配置)
# 4. 插件会自动处理连接和项目同步
#
# 备用方案: 如需直接 SSH 连接, 请参考输出中的 ssh_connection 信息.

# 创建 CVM 实例
resource "tencentcloud_instance" "coder_instance" {
  # 基本配置
  instance_name     = "coder-${data.coder_workspace_owner.me.name}-${data.coder_workspace.me.name}"
  availability_zone = local.availability_zone
  image_id          = local.image_id
  instance_type     = data.coder_parameter.instance_type.value
  hostname          = "coder-${substr(data.coder_workspace.me.id, 0, 8)}"
  project_id        = local.project_id

  # 计费配置
  instance_charge_type = data.coder_parameter.instance_charge_type.value

  # 竞价实例配置 (选择竞价实例时使用用户自定义价格)
  spot_instance_type = data.coder_parameter.instance_charge_type.value == "SPOTPAID" ? "ONE-TIME" : null
  spot_max_price     = data.coder_parameter.instance_charge_type.value == "SPOTPAID" ? data.coder_parameter.spot_max_price.value : null

  # 网络配置 (使用固定配置)
  vpc_id                     = local.vpc_id
  subnet_id                  = local.subnet_id
  orderly_security_groups    = [local.security_group_id]
  allocate_public_ip         = local.allocate_public_ip
  internet_charge_type       = local.internet_charge_type
  internet_max_bandwidth_out = local.internet_max_bandwidth_out

  # 存储配置 (使用固定配置)
  system_disk_type = local.system_disk_type
  system_disk_size = local.system_disk_size

  # SSH 密钥认证 (使用固定配置)
  key_ids = [local.ssh_key_id]

  # 用户数据脚本 - 安装 Coder Agent
  user_data = base64encode(templatefile("${path.module}/scripts/user_data.sh", {
    init_script = base64encode(coder_agent.main.init_script)
    coder_agent_token = coder_agent.main.token
    coder_server_ip = var.coder_server_ip
    coder_server_domain = var.coder_server_domain
  }))

  # 服务配置 (按要求禁用相关服务)
  disable_security_service   = true  # 不启用安全加固
  disable_monitor_service    = true  # 不启用云监控
  disable_automation_service = true  # 不启用自动化助手

  # 标签配置
  tags = {
    "Coder"        = "true"
    "Owner"        = data.coder_workspace_owner.me.name
    "Workspace"    = data.coder_workspace.me.name
    "InstanceType" = data.coder_parameter.instance_type.value
    "ChargeType"   = data.coder_parameter.instance_charge_type.value
    "Environment"  = "Development"
    "CreatedBy"    = "Terraform"
  }
}