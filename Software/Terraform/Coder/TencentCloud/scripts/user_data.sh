#!/bin/bash
set -e

# Coder Agent 安装和启动脚本
# 此脚本在 CVM 实例首次启动时执行 (以 root 用户运行)
# 支持私有 Coder 服务器部署

echo "🚀 开始安装 Coder Agent..."

# 配置本地 DNS 解析 (如果 Coder 服务未在公网解析)
echo "🔧 配置 DNS 解析..."
# 添加 Coder 服务 DNS 解析记录
# 格式: echo "CODER_SERVER_IP coder.yourcompany.com" >> /etc/hosts
if [ -n "${coder_server_ip}" ] && [ -n "${coder_server_domain}" ]; then
    echo "${coder_server_ip} ${coder_server_domain}" >> /etc/hosts
    echo "✅ 已添加 Coder 服务本地 DNS 解析: ${coder_server_ip} ${coder_server_domain}"
fi

# 安装 Coder Agent 运行的最小依赖
echo "📦 安装 Coder Agent 基础依赖..."
apt-get update -qq
apt-get install -y -qq \
    curl \
    wget \
    systemd \
    dnsutils

# 验证网络连通性 (针对私有 Coder 服务)
if [ -n "${coder_server_domain}" ]; then
    echo "🌐 验证 Coder 服务的连通性..."
    if nslookup "${coder_server_domain}" >/dev/null 2>&1; then
        echo "✅ DNS 解析成功: ${coder_server_domain}"
    else
        echo "⚠️  DNS 解析失败: ${coder_server_domain}"
    fi

    if curl --connect-timeout 10 "https://${coder_server_domain}" >/dev/null 2>&1; then
        echo "✅ HTTPS 连接成功: ${coder_server_domain}"
    else
        echo "⚠️  HTTPS 连接失败, 但继续尝试 Agent 安装."
    fi
fi

# 直接以 root 用户安装和运行 Coder Agent
cd /root

# 解码并执行 Coder Agent 初始化脚本
echo "${init_script}" | base64 -d > /tmp/coder_init.sh
chmod +x /tmp/coder_init.sh

# 设置 Coder Agent Token 环境变量
export CODER_AGENT_TOKEN="${coder_agent_token}"

# 执行初始化脚本
/tmp/coder_init.sh

# 清理临时文件
rm -f /tmp/coder_init.sh

echo "✅ Coder Agent 安装完成"
echo "💡 系统环境和 JuiceFS 将在 Agent 启动后配置"
