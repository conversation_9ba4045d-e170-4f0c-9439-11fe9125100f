#!/bin/bash
set -e

# Coder Agent 安装和启动脚本
# 此脚本在 CVM 实例首次启动时执行 (以 root 用户运行)

echo "🚀 开始安装 Coder Agent..."

# 安装 Coder Agent 运行的最小依赖
echo "📦 安装 Coder Agent 基础依赖..."
apt-get update -qq
apt-get install -y -qq \
    curl \
    wget \
    systemd

# 直接以 root 用户安装和运行 Coder Agent
cd /root

# 解码并执行 Coder Agent 初始化脚本
echo "${init_script}" | base64 -d > /tmp/coder_init.sh
chmod +x /tmp/coder_init.sh

# 设置 Coder Agent Token 环境变量
export CODER_AGENT_TOKEN="${coder_agent_token}"

# 执行初始化脚本
/tmp/coder_init.sh

# 清理临时文件
rm -f /tmp/coder_init.sh

echo "✅ Coder Agent 安装完成"
echo "💡 系统环境和 JuiceFS 将在 Agent 启动后配置"
