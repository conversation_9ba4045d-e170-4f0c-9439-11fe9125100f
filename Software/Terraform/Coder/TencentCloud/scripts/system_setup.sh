#!/bin/bash
set -e

# 系统环境配置脚本

echo "🔧 开始配置系统开发环境..."

# 更新系统包管理器和环境
echo "📦 更新系统包管理器和环境..."
apt-get update -qq
apt-get upgrade -y -qq

# 安装基础系统工具
echo "🛠️ 安装基础系统工具..."
apt-get install -y -qq \
    ca-certificates \
    gnupg \
    lsb-release

# 安装开发基础工具
echo "🔨 安装开发基础工具..."
apt-get install -y -qq \
    gcc \
    g++ \
    git \
    build-essential \
    pkg-config \
    libssl-dev \
    unzip \
    zip \
    btop \
    tree \
    jq \
    pigz \
    nano

# 安装网络和调试工具
echo "🌐 安装网络和调试工具..."
apt-get install -y -qq \
    net-tools \
    iputils-ping \
    telnet \
    dnsutils \
    tcpdump \
    strace \
    lsof

# 安装 Rust 开发环境 (可选)
echo "🦀 安装 Rust 开发环境..."
apt-get install -y cpanminus
cpanm -q --notest FindBin
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
source ~/.cargo/env
~/.cargo/bin/cargo install cross
rustup component add rust-analyzer

# 安装 Node.js 开发环境 (可选)
echo "📦 安装 Node.js 开发环境..."
curl -fsSL https://deb.nodesource.com/setup_lts.x | bash -
apt-get install -y nodejs

# 安装 Python 开发环境 (可选)
echo "🐍 安装 Python 开发环境..."
apt-get install -y -qq \
    python3 \
    python3-pip \
    python3-venv \
    python3-dev

# 安装 Docker (可选)
echo "🐳 安装 Docker..."
curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
apt-get update -qq
apt-get install -y -qq docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 清理包管理器缓存
echo "🧹 清理系统缓存..."
apt-get autoremove -y -qq
apt-get autoclean -qq

echo "✅ 系统开发环境配置完成!"
