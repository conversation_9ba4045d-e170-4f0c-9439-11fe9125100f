#!/bin/bash
set -e

# 系统环境配置脚本

echo "🔧 开始配置系统开发环境..."

# 更新系统包管理器和环境
echo "📦 更新系统包管理器和环境..."
apt-get update -qq
apt-get upgrade -y -qq

# 安装基础系统工具
echo "🛠️ 安装基础系统工具..."
apt-get install -y -qq \
    ca-certificates \
    gnupg \
    lsb-release

# 安装开发基础工具
echo "🔨 安装开发基础工具..."
apt-get install -y -qq \
    gcc \
    g++ \
    git \
    build-essential \
    pkg-config \
    libssl-dev \
    unzip \
    zip \
    btop \
    tree \
    jq \
    pigz \
    nano

# 安装网络和调试工具
echo "🌐 安装网络和调试工具..."
apt-get install -y -qq \
    net-tools \
    iputils-ping \
    telnet \
    dnsutils \
    tcpdump \
    strace \
    lsof

# 安装 Rust 开发环境 (可选)
echo "🦀 安装 Rust 开发环境..."
apt-get install -y cpanminus
cpanm -q --notest FindBin
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
source ~/.cargo/env
~/.cargo/bin/cargo install cross
rustup component add rust-analyzer

# 安装 Node.js 开发环境 (可选)
echo "📦 安装 Node.js 开发环境..."
curl -fsSL https://deb.nodesource.com/setup_lts.x | bash -
apt-get install -y nodejs

# 安装 Python 开发环境 (可选)
echo "🐍 安装 Python 开发环境..."
apt-get install -y -qq \
    python3 \
    python3-pip \
    python3-venv \
    python3-dev

# 安装 Docker (增强版本，修复安装失败问题)
echo "🐳 安装 Docker..."

# 检查系统版本
echo "📋 系统信息: $(lsb_release -ds)"
echo "📋 架构信息: $(dpkg --print-architecture)"

# 安装必要的依赖
echo "📦 安装Docker依赖..."
apt-get install -y ca-certificates curl gnupg lsb-release

# 添加Docker官方GPG密钥
echo "🔑 添加Docker GPG密钥..."
mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg
chmod a+r /etc/apt/keyrings/docker.gpg

# 添加Docker仓库
echo "📦 添加Docker仓库..."
echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null

# 更新包索引
echo "🔄 更新包索引..."
apt-get update

# 安装Docker
echo "⬇️  安装Docker组件..."
apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# 验证Docker安装
echo "✅ 验证Docker安装..."
if command -v docker >/dev/null 2>&1; then
    echo "✅ Docker安装成功: $(docker --version)"
    # 启动Docker服务
    systemctl enable docker
    systemctl start docker
    echo "✅ Docker服务已启动"
else
    echo "❌ Docker安装失败"
    exit 1
fi

# 清理包管理器缓存
echo "🧹 清理系统缓存..."
apt-get autoremove -y -qq
apt-get autoclean -qq

echo "✅ 系统开发环境配置完成!"
