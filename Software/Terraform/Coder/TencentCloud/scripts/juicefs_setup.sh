#!/bin/bash
set -e

# JuiceFS 云服务配置脚本

echo "💾 开始配置 JuiceFS 云服务..."

# 检查必要的环境变量（支持内联调用）
if [ -z "$FILESYSTEM_NAME" ] || [ -z "$JUICEFS_TOKEN" ] || [ -z "$COS_ACCESS_KEY" ] || [ -z "$COS_SECRET_KEY" ] || [ -z "$COS_ENDPOINT" ]; then
    # 如果环境变量不存在，尝试使用命令行参数（向后兼容）
    if [ -n "$1" ] && [ -n "$2" ] && [ -n "$3" ] && [ -n "$4" ] && [ -n "$5" ]; then
        FILESYSTEM_NAME="$1"
        JUICEFS_TOKEN="$2"
        COS_ACCESS_KEY="$3"
        COS_SECRET_KEY="$4"
        COS_ENDPOINT="$5"
    else
        echo "❌ 错误: 缺少必要的参数"
        echo "请设置环境变量: FILESYSTEM_NAME, JUICEFS_TOKEN, COS_ACCESS_KEY, COS_SECRET_KEY, COS_ENDPOINT"
        echo "或使用命令行参数: $0 <filesystem_name> <token> <access_key> <secret_key> <endpoint>"
        exit 1
    fi
fi

echo "📋 JuiceFS 配置参数:"
echo "   文件系统名称: $FILESYSTEM_NAME"
echo "   COS Endpoint: $COS_ENDPOINT"

# 开始安装 JuiceFS
echo "📥 开始安装 JuiceFS..."
curl -sSL https://juicefs.com/static/juicefs -o /usr/local/bin/juicefs && chmod +x /usr/local/bin/juicefs

# 验证 JuiceFS 安装
if [ ! -x "/usr/local/bin/juicefs" ]; then
    echo "❌ JuiceFS 安装失败"
    exit 1
fi

echo "✅ JuiceFS 安装成功"
/usr/local/bin/juicefs version

# 配置 JuiceFS 认证
echo "🔐 配置 JuiceFS 认证..."
/usr/local/bin/juicefs auth "$FILESYSTEM_NAME" \
    --token "$JUICEFS_TOKEN" \
    --accesskey "$COS_ACCESS_KEY" \
    --secretkey "$COS_SECRET_KEY" \
    --bucket "$COS_ENDPOINT"

if [ $? -ne 0 ]; then
    echo "❌ JuiceFS 认证失败, 请检查参数配置."
    exit 1
fi

echo "✅ JuiceFS 认证成功"

# 创建挂载点
echo "📁 创建工作空间挂载点..."
mkdir -p /data && mkdir -p /data/workspaces

# 挂载 JuiceFS 文件系统
echo "🔗 挂载 JuiceFS 文件系统..."
/usr/local/bin/juicefs mount "$FILESYSTEM_NAME" /data/workspaces \
    --update-fstab \
    --cache-size=10240

# 等待 5 秒挂载完成
sleep 5

# 验证挂载状态
if mountpoint -q /data/workspaces; then
    echo "✅ JuiceFS 文件系统挂载成功"
    echo "📂 挂载点: /data/workspaces"
else
    echo "❌ JuiceFS 文件系统挂载失败"
    exit 1
fi

# 创建软链接
echo "🔗 创建软链接..."
ln -sf /data/workspaces /root/workspace

# 显示挂载信息
echo ""
echo "📊 JuiceFS 挂载信息:"
df -h /data/workspaces | tail -n 1
echo ""
echo "🎉 JuiceFS 配置完成!"
echo "📂 工作空间路径: /data/workspaces"
echo "🔗 快捷访问: /root/workspace"
echo "💾 缓存大小: 10GB"
echo "🔄 自动挂载: 已启用 (重启后自动挂载)"