# ==================== 实例基本信息 ====================
output "instance_id" {
  description = "实例 ID"
  value       = tencentcloud_instance.coder_instance.id
}

output "instance_name" {
  description = "实例名称"
  value       = tencentcloud_instance.coder_instance.instance_name
}

output "instance_type" {
  description = "实例规格"
  value       = tencentcloud_instance.coder_instance.instance_type
}

# ==================== 网络连接信息 ====================
output "public_ip" {
  description = "公有 IP"
  value       = tencentcloud_instance.coder_instance.public_ip
}

output "private_ip" {
  description = "私有 IP"
  value       = tencentcloud_instance.coder_instance.private_ip
}

# ==================== Coder Agent 连接信息 ====================
output "coder_agent_token" {
  description = "Coder Agent 连接令牌"
  value       = coder_agent.main.token
  sensitive   = true
}

output "coder_agent_init_script" {
  description = "Coder Agent 初始化脚本"
  value       = coder_agent.main.init_script
  sensitive   = true
}

# ==================== SSH 连接信息 ====================
output "ssh_connection" {
  description = "使用 SSH 连接"
  value       = "ssh -i ~/.ssh/coder-key.pem root@${tencentcloud_instance.coder_instance.public_ip}"
}

output "ssh_config" {
  description = "SSH 配置信息"
  value = {
    host     = tencentcloud_instance.coder_instance.public_ip
    port     = 22
    username = "root"  # CVM 实例默认用户
    key_file = "~/.ssh/coder-key.pem"
  }
}

# ==================== JuiceFS 工作空间信息 ====================
output "juicefs_mount_point" {
  description = "JuiceFS 挂载点路径"
  value       = "/data/workspaces"
}

output "shared_workspace_path" {
  description = "共享工作空间路径"
  value       = "/data/workspaces"
}

output "workspace_shortcut" {
  description = "工作空间快捷访问路径"
  value       = "/root/workspace"
}

# ==================== 配置摘要 ====================
output "configuration_summary" {
  description = "实例配置摘要"
  value = {
    region            = local.region
    availability_zone = local.availability_zone
    instance_type     = data.coder_parameter.instance_type.value
    image_id          = local.image_id
    charge_type       = data.coder_parameter.instance_charge_type.value
    system_disk_type  = local.system_disk_type
    system_disk_size  = local.system_disk_size
    vpc_id           = local.vpc_id
    subnet_id        = local.subnet_id
    security_group_id = local.security_group_id
    juicefs_filesystem = var.juicefs_vol_name
    juicefs_mount_point = "/data/workspaces"
    shared_workspace = "/data/workspaces"
  }
}
