# 针对具体错误的修复方案

## 🎯 **确认的错误分析**

基于您提供的具体错误信息，我们确认了两个关键问题：

### **错误1: 脚本路径问题**
```
🚀 开始初始化 Coder 工作空间...
🔧 第一阶段: 配置系统开发环境...
bash: ./scripts/system_setup.sh: No such file or directory
```

**根本原因：** 脚本文件没有传输到远程CVM实例

### **错误2: Docker命令未找到**
```
"run docker ps: exit status 127: \"/bin/bash: line 1: docker: command not found\": exit status 127"
```

**根本原因：** Docker安装失败，导致docker命令不可用

## 🔧 **精确修复方案**

### **修复1: 实施内联脚本方案**

**修改文件：** `tencentcloud.tf`

**解决方法：** 使用Terraform的`file()`和`indent()`函数直接将脚本内容嵌入到`startup_script`中

```hcl
startup_script = <<-EOT
  #!/bin/bash
  set -e
  
  echo "🚀 开始初始化 Coder 工作空间..."
  
  # === 直接嵌入 system_setup.sh 的内容 ===
  ${indent(4, file("${path.module}/scripts/system_setup.sh"))}
  
  # === 直接嵌入 juicefs_setup.sh 的内容 ===
  export FILESYSTEM_NAME="${var.juicefs_vol_name}"
  # ... 其他环境变量
  ${indent(4, file("${path.module}/scripts/juicefs_setup.sh"))}
EOT
```

**效果：** 消除"No such file or directory"错误

### **修复2: 增强Docker安装脚本**

**修改文件：** `scripts/system_setup.sh`

**主要改进：**
1. **更新的GPG密钥路径：** 使用`/etc/apt/keyrings/docker.gpg`而不是旧的路径
2. **增强的依赖安装：** 确保所有必要的依赖都已安装
3. **详细的调试信息：** 添加系统信息和安装过程日志
4. **Docker服务启动：** 确保Docker服务正确启动
5. **安装验证：** 验证Docker命令是否可用

**关键修复：**
```bash
# 新的GPG密钥路径
mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# 更新的仓库配置
echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list

# 安装验证和服务启动
if command -v docker >/dev/null 2>&1; then
    systemctl enable docker
    systemctl start docker
    echo "✅ Docker安装成功: $(docker --version)"
else
    echo "❌ Docker安装失败"
    exit 1
fi
```

### **修复3: JuiceFS脚本环境变量支持**

**修改文件：** `scripts/juicefs_setup.sh`

**改进：** 支持环境变量传参，兼容内联调用方式

```bash
# 检查环境变量（优先）或命令行参数（向后兼容）
if [ -z "$FILESYSTEM_NAME" ] || [ -z "$JUICEFS_TOKEN" ]; then
    if [ -n "$1" ] && [ -n "$2" ]; then
        FILESYSTEM_NAME="$1"
        JUICEFS_TOKEN="$2"
        # ... 其他参数
    else
        echo "❌ 错误: 缺少必要的参数"
        exit 1
    fi
fi
```

## 🧪 **验证和调试**

### **创建的调试工具：**
1. **`debug_startup_script.sh`** - 验证内联脚本效果
2. **增强的错误日志** - 详细的安装过程信息

### **验证步骤：**
1. **部署更新的模板**
2. **创建新的工作空间**
3. **观察初始化日志**：
   - 应该看到详细的Docker安装过程
   - 不应该再出现"No such file or directory"错误
4. **SSH到CVM验证**：
   ```bash
   # 检查Docker是否可用
   docker --version
   docker ps
   
   # 检查JuiceFS挂载
   mountpoint /data/workspaces
   df -h /data/workspaces
   ```

## 🎯 **预期效果**

### **修复后应该看到：**
1. ✅ 不再出现脚本文件路径错误
2. ✅ Docker成功安装并可用
3. ✅ JuiceFS正确挂载
4. ✅ 工作空间健康状态正常
5. ✅ 监控项正常显示数据

### **如果仍有问题：**
1. **检查网络连通性** - 确保能访问Docker和JuiceFS的服务
2. **查看详细日志** - 使用调试脚本收集更多信息
3. **验证权限** - 确保脚本有足够的权限执行安装操作

## 📋 **部署清单**

修改的文件：
- ✅ `tencentcloud.tf` - 内联脚本实现
- ✅ `scripts/system_setup.sh` - 增强Docker安装
- ✅ `scripts/juicefs_setup.sh` - 环境变量支持
- ✅ `debug_startup_script.sh` - 调试工具

这些修复直接针对您发现的具体错误，应该能够解决脚本执行和Docker安装的问题。
